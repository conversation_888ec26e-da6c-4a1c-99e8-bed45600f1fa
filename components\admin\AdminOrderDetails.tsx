"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { 
  X,
  User,
  Mail,
  Phone,
  Package,
  Calendar,
  Clock,
  DollarSign,
  CheckCircle,
  XCircle,
  AlertCircle,
  <PERSON>rash2,
  <PERSON>,
  Save,
  <PERSON>r<PERSON><PERSON><PERSON>,
  Flag,
  MessageSquare,

  Copy,
  Download
} from "lucide-react"
import { 
  ProductOrder, 
  OrderStatus, 
  DynamicField,
  OrderEvent 
} from "@/lib/types"
import { 
  updateProductOrderStatus, 
  deleteProductOrder 
} from "@/lib/utils/orderStorage"
import {
  updateOrderStatus,
  formatFieldDataForDisplay
} from "@/lib/utils/orderUtils"
import {
  migrateOrderFieldData,
  needsMigration,
  getMigrationStats
} from "@/lib/utils/fieldMigration"
import { SimpleOrderChatWidget } from "./SimpleOrderChatWidget"
import {
  getOrderChatContext,
  sendOrderStatusUpdateMessage,
  markOrderMessagesAsRead
} from "@/lib/utils/orderChatAccess"
import { formatCurrency } from "@/lib/data/currencies"
import { formatDate, formatDateTime } from "@/lib/utils/dateUtils"

interface AdminOrderDetailsProps {
  order: ProductOrder
  onClose: () => void
  onOrderUpdate: () => void
  userRole?: "admin" | "moderator" | "viewer"
}

export function AdminOrderDetails({ 
  order, 
  onClose, 
  onOrderUpdate, 
  userRole = "admin" 
}: AdminOrderDetailsProps) {
  // State management
  const [activeTab, setActiveTab] = useState<"details" | "timeline" | "actions" | "chat">("details")
  const [isEditing, setIsEditing] = useState(false)
  const [adminNotes, setAdminNotes] = useState(order.adminNotes || "")
  const [internalNotes, setInternalNotes] = useState(order.internalNotes || "")
  const [selectedStatus, setSelectedStatus] = useState<OrderStatus>(order.status)
  const [selectedPriority, setSelectedPriority] = useState(order.priority || "normal")
  const [assignedAdmin, setAssignedAdmin] = useState(order.assignedAdminId || "")

  const [isLoading, setIsLoading] = useState(false)
  const [showChat, setShowChat] = useState(false)
  const [migrationInfo, setMigrationInfo] = useState<any>(null)
  const [showMigrationDetails, setShowMigrationDetails] = useState(false)

  // Check for field migration needs
  const checkMigrationNeeds = () => {
    if (order.productData && typeof order.productData === 'object') {
      const needsAnyMigration = Object.values(order.productData).some((fieldData: any) =>
        needsMigration(fieldData)
      )

      if (needsAnyMigration) {
        const stats = getMigrationStats(order.productData as any)
        setMigrationInfo({
          needsMigration: true,
          stats,
          canMigrate: userRole === "admin"
        })
      }
    }
  }

  // Initialize migration check
  useState(() => {
    checkMigrationNeeds()
  })

  // Chat integration
  const handleOpenChat = () => {
    setShowChat(true)
    // TODO: Initialize chat with order context
  }

  // Status management
  const handleStatusUpdate = async (newStatus: OrderStatus, notes?: string) => {
    if (userRole === "viewer") return

    setIsLoading(true)
    try {
      // ## Supabase Integration: Replace with supabase update
      updateProductOrderStatus(order.id, newStatus, notes, "admin_current")
      onOrderUpdate()
      
      // Show success message
      alert(`تم تحديث حالة الطلب إلى "${getStatusLabel(newStatus)}" بنجاح`)
    } catch (error) {
      console.error("Error updating order status:", error)
      alert("حدث خطأ أثناء تحديث حالة الطلب")
    } finally {
      setIsLoading(false)
    }
  }

  // Delete order
  const handleDeleteOrder = async () => {
    if (userRole !== "admin") return

    setIsLoading(true)
    try {
      // ## Supabase Integration: Replace with supabase delete
      deleteProductOrder(order.id)
      onOrderUpdate()
      onClose()
      
      alert("تم حذف الطلب بنجاح")
    } catch (error) {
      console.error("Error deleting order:", error)
      alert("حدث خطأ أثناء حذف الطلب")
    } finally {
      setIsLoading(false)
    }
  }

  // Save notes and other updates
  const handleSaveUpdates = async () => {
    if (userRole === "viewer") return

    setIsLoading(true)
    try {
      // ## Supabase Integration: Replace with supabase update
      // For now, we'll update localStorage
      const orders = JSON.parse(localStorage.getItem("product_orders") || "[]")
      const orderIndex = orders.findIndex((o: ProductOrder) => o.id === order.id)
      
      if (orderIndex !== -1) {
        orders[orderIndex] = {
          ...orders[orderIndex],
          adminNotes,
          internalNotes,
          priority: selectedPriority,
          assignedAdminId: assignedAdmin,
          updatedAt: new Date()
        }
        localStorage.setItem("product_orders", JSON.stringify(orders))
      }

      onOrderUpdate()
      setIsEditing(false)
      alert("تم حفظ التحديثات بنجاح")
    } catch (error) {
      console.error("Error saving updates:", error)
      alert("حدث خطأ أثناء حفظ التحديثات")
    } finally {
      setIsLoading(false)
    }
  }

  // Helper functions
  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case "pending":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
      case "processing":
        return "bg-blue-500/20 text-blue-400 border-blue-500/30"
      case "completed":
        return "bg-green-500/20 text-green-400 border-green-500/30"
      case "failed":
        return "bg-red-500/20 text-red-400 border-red-500/30"
      case "cancelled":
        return "bg-gray-500/20 text-gray-400 border-gray-500/30"
      default:
        return "bg-slate-500/20 text-slate-400 border-slate-500/30"
    }
  }

  const getStatusIcon = (status: OrderStatus) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4" />
      case "processing":
        return <AlertCircle className="h-4 w-4" />
      case "completed":
        return <CheckCircle className="h-4 w-4" />
      case "failed":
      case "cancelled":
        return <XCircle className="h-4 w-4" />
      default:
        return <Package className="h-4 w-4" />
    }
  }

  const getStatusLabel = (status: OrderStatus) => {
    const labels: Record<OrderStatus, string> = {
      pending: "في الانتظار",
      processing: "قيد المعالجة",
      completed: "مكتمل",
      failed: "فشل",
      cancelled: "ملغي"
    }
    return labels[status]
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red-500/20 text-red-400 border-red-500/30"
      case "high":
        return "bg-orange-500/20 text-orange-400 border-orange-500/30"
      case "normal":
        return "bg-blue-500/20 text-blue-400 border-blue-500/30"
      case "low":
        return "bg-gray-500/20 text-gray-400 border-gray-500/30"
      default:
        return "bg-slate-500/20 text-slate-400 border-slate-500/30"
    }
  }

  const getPriorityLabel = (priority: string) => {
    const labels: Record<string, string> = {
      urgent: "عاجل",
      high: "عالي",
      normal: "عادي",
      low: "منخفض"
    }
    return labels[priority] || "عادي"
  }

  // Enhanced field value formatting
  const formatFieldValue = (fieldName: string, value: any): React.ReactNode => {
    if (!value) return <span className="text-slate-500 italic">لا توجد بيانات</span>

    // Handle different value types
    if (typeof value === "object") {
      if (value.name && value.price) {
        // Package selector format
        return (
          <div className="space-y-1">
            <p className="text-white font-medium">{value.name}</p>
            <p className="text-green-400 text-sm">{formatCurrency(value.price, order.pricing.currency)}</p>
            {value.description && (
              <p className="text-slate-400 text-xs">{value.description}</p>
            )}
          </div>
        )
      } else {
        // Generic object format with better structure
        return (
          <div className="bg-slate-800/50 rounded p-2">
            <pre className="text-xs text-slate-300 whitespace-pre-wrap break-words">
              {JSON.stringify(value, null, 2)}
            </pre>
          </div>
        )
      }
    }

    // Handle boolean values
    if (typeof value === "boolean") {
      return (
        <div className="flex items-center gap-2">
          {value ? (
            <>
              <CheckCircle className="h-4 w-4 text-green-400" />
              <span className="text-green-400">نعم</span>
            </>
          ) : (
            <>
              <XCircle className="h-4 w-4 text-red-400" />
              <span className="text-red-400">لا</span>
            </>
          )}
        </div>
      )
    }

    // Handle file uploads
    if (fieldName.includes("image") || fieldName.includes("file")) {
      return (
        <div className="flex items-center gap-2">
          <div className="p-2 bg-blue-500/20 rounded">
            <Download className="h-4 w-4 text-blue-400" />
          </div>
          <div>
            <p className="text-blue-400 text-sm">ملف مرفق</p>
            <p className="text-slate-400 text-xs">انقر للتحميل</p>
          </div>
        </div>
      )
    }

    // Handle URLs
    if (typeof value === "string" && (value.startsWith("http") || value.startsWith("www"))) {
      return (
        <a
          href={value}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-400 hover:text-blue-300 underline break-all"
        >
          {value}
        </a>
      )
    }

    // Default string format with proper text handling
    return <span className="text-white break-words">{String(value)}</span>
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-2 sm:p-4 z-50">
      <Card className="bg-slate-800 border-slate-700 w-full max-w-6xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden">
        {/* Header */}
        <CardHeader className="border-b border-slate-700 p-3 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
            <div className="min-w-0 flex-1">
              <CardTitle className="text-white flex items-center gap-2 text-lg sm:text-xl">
                <Package className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                <span className="truncate">تفاصيل الطلب: {order.id}</span>
              </CardTitle>
              <div className="flex flex-wrap items-center gap-2 mt-2">
                <Badge className={`${getStatusColor(order.status)} text-xs sm:text-sm`}>
                  {getStatusIcon(order.status)}
                  <span className="mr-1">{getStatusLabel(order.status)}</span>
                </Badge>
                <Badge className={`${getPriorityColor(order.priority || "normal")} text-xs sm:text-sm`}>
                  <Flag className="h-3 w-3 mr-1" />
                  {getPriorityLabel(order.priority || "normal")}
                </Badge>
                <Badge variant="outline" className="text-slate-300 text-xs sm:text-sm">
                  {order.processingType === "instant" ? "معالجة فورية" : "معالجة يدوية"}
                </Badge>
              </div>
            </div>
            <div className="flex items-center gap-2 flex-shrink-0">
              {userRole !== "viewer" && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditing(!isEditing)}
                  className="border-slate-600 text-slate-300 text-xs sm:text-sm"
                >
                  <Edit className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                  <span className="hidden sm:inline">{isEditing ? "إلغاء التعديل" : "تعديل"}</span>
                  <span className="sm:hidden">{isEditing ? "إلغاء" : "تعديل"}</span>
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="text-slate-400 hover:text-white"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        {/* Content */}
        <CardContent className="p-0 overflow-y-auto max-h-[calc(95vh-120px)] sm:max-h-[calc(90vh-120px)]">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
            <TabsList className="grid w-full grid-cols-4 bg-slate-800/50 m-2 sm:m-4 mb-0">
              <TabsTrigger
                value="details"
                className="data-[state=active]:bg-slate-700 text-xs sm:text-sm px-1 sm:px-3"
              >
                <span className="hidden sm:inline">تفاصيل الطلب</span>
                <span className="sm:hidden">التفاصيل</span>
              </TabsTrigger>
              <TabsTrigger
                value="chat"
                className="data-[state=active]:bg-slate-700 text-xs sm:text-sm px-1 sm:px-3"
              >
                <MessageSquare className="h-3 w-3 sm:h-4 sm:w-4 ml-1" />
                <span className="hidden sm:inline">المحادثة</span>
                <span className="sm:hidden">دردشة</span>
              </TabsTrigger>
              <TabsTrigger
                value="timeline"
                className="data-[state=active]:bg-slate-700 text-xs sm:text-sm px-1 sm:px-3"
              >
                <span className="hidden sm:inline">سجل الأحداث</span>
                <span className="sm:hidden">الأحداث</span>
              </TabsTrigger>
              <TabsTrigger
                value="actions"
                className="data-[state=active]:bg-slate-700 text-xs sm:text-sm px-1 sm:px-3"
              >
                <span className="hidden sm:inline">إجراءات الإدارة</span>
                <span className="sm:hidden">الإجراءات</span>
              </TabsTrigger>
            </TabsList>

            {/* Order Details Tab */}
            <TabsContent value="details" className="p-2 sm:p-4 space-y-4 sm:space-y-6">
              {/* Customer Information */}
              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader className="p-3 sm:p-6">
                  <CardTitle className="text-white text-base sm:text-lg flex items-center gap-2">
                    <User className="h-4 w-4 sm:h-5 sm:w-5" />
                    معلومات العميل
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 p-3 sm:p-6">
                  <div className="space-y-1">
                    <Label className="text-slate-400 text-xs sm:text-sm">الاسم الكامل</Label>
                    <p className="text-white font-medium text-sm sm:text-base break-words">{order.userDetails.fullName}</p>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-slate-400 text-xs sm:text-sm">البريد الإلكتروني</Label>
                    <div className="flex items-center gap-2">
                      <p className="text-white font-medium text-sm sm:text-base break-all flex-1 min-w-0">{order.userDetails.email}</p>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(order.userDetails.email)
                          alert("تم نسخ البريد الإلكتروني")
                        }}
                        className="h-6 w-6 p-0 text-slate-400 hover:text-white flex-shrink-0"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-slate-400 text-xs sm:text-sm">رقم الهاتف</Label>
                    <div className="flex items-center gap-2">
                      <p className="text-white font-medium text-sm sm:text-base break-all flex-1 min-w-0">{order.userDetails.phone}</p>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(order.userDetails.phone)
                          alert("تم نسخ رقم الهاتف")
                        }}
                        className="h-6 w-6 p-0 text-slate-400 hover:text-white flex-shrink-0"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Product Information */}
              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader className="p-3 sm:p-6">
                  <CardTitle className="text-white text-base sm:text-lg flex items-center gap-2">
                    <Package className="h-4 w-4 sm:h-5 sm:w-5" />
                    معلومات المنتج
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 p-3 sm:p-6">
                  <div className="space-y-1">
                    <Label className="text-slate-400 text-xs sm:text-sm">اسم المنتج</Label>
                    <p className="text-white font-medium text-sm sm:text-base break-words">{order.templateName}</p>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-slate-400 text-xs sm:text-sm">الفئة</Label>
                    <p className="text-white font-medium text-sm sm:text-base">{order.templateCategory}</p>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-slate-400 text-xs sm:text-sm">تاريخ الإنشاء</Label>
                    <p className="text-white font-medium text-sm sm:text-base">
                      {formatDateTime(order.createdAt)}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-slate-400 text-xs sm:text-sm">آخر تحديث</Label>
                    <p className="text-white font-medium text-sm sm:text-base">
                      {formatDateTime(order.updatedAt)}
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Pricing Information */}
              <Card className="bg-gradient-to-br from-slate-700/50 to-slate-800/50 border-slate-600 shadow-lg">
                <CardHeader className="p-3 sm:p-6">
                  <CardTitle className="text-white text-base sm:text-lg flex items-center gap-2">
                    <div className="p-2 bg-green-500/20 rounded-lg">
                      <DollarSign className="h-4 w-4 sm:h-5 sm:w-5 text-green-400" />
                    </div>
                    تفاصيل السعر
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3 sm:space-y-4 p-3 sm:p-6">
                  {order.pricing.basePrice > 0 && (
                    <div className="flex justify-between items-center p-3 bg-slate-800/50 rounded-lg">
                      <span className="text-slate-400 text-sm sm:text-base">السعر الأساسي:</span>
                      <span className="text-white font-medium text-sm sm:text-base">
                        {formatCurrency(order.pricing.basePrice, order.pricing.currency)}
                      </span>
                    </div>
                  )}

                  {order.pricing.modifiers.map((modifier, index) => (
                    <div key={index} className="flex justify-between items-center p-3 bg-slate-800/30 rounded-lg">
                      <span className="text-slate-400 text-sm sm:text-base">{modifier.fieldLabel}:</span>
                      <span className={`font-medium text-sm sm:text-base ${
                        modifier.type === "add" ? "text-green-400" : "text-blue-400"
                      }`}>
                        {modifier.type === "add" ? "+" : ""}
                        {formatCurrency(modifier.modifier, order.pricing.currency)}
                      </span>
                    </div>
                  ))}

                  {order.pricing.quantity > 1 && (
                    <div className="flex justify-between items-center p-3 bg-slate-800/30 rounded-lg">
                      <span className="text-slate-400 text-sm sm:text-base">الكمية:</span>
                      <span className="text-blue-400 font-medium text-sm sm:text-base">× {order.pricing.quantity}</span>
                    </div>
                  )}

                  <div className="border-t border-slate-600 pt-4 mt-4">
                    <div className="flex justify-between items-center p-4 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-lg border border-yellow-500/30">
                      <span className="text-yellow-400 font-bold text-base sm:text-lg">الإجمالي:</span>
                      <span className="text-yellow-400 font-bold text-lg sm:text-xl">
                        {formatCurrency(order.pricing.totalPrice, order.pricing.currency)}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Field Data Display */}
              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader className="p-3 sm:p-6">
                  <CardTitle className="text-white text-base sm:text-lg flex items-center gap-2">
                    <Edit className="h-4 w-4 sm:h-5 sm:w-5" />
                    بيانات النموذج
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-3 sm:p-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                    {Object.entries(order.productData).map(([fieldName, fieldValue]) => (
                      <div key={fieldName} className="space-y-2">
                        <Label className="text-slate-400 text-xs sm:text-sm font-medium">
                          {fieldName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </Label>
                        <div className="bg-slate-800/50 rounded-lg p-3 min-h-[2.5rem] flex items-start">
                          <div className="w-full">
                            {formatFieldValue(fieldName, fieldValue)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Actions Row */}
                  <div className="mt-6 pt-4 border-t border-slate-600">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const dataStr = JSON.stringify(order.productData, null, 2)
                        const blob = new Blob([dataStr], { type: 'application/json' })
                        const url = URL.createObjectURL(blob)
                        const a = document.createElement('a')
                        a.href = url
                        a.download = `order-${order.id}-data.json`
                        a.click()
                        URL.revokeObjectURL(url)
                        alert("تم تحميل ملف البيانات")
                      }}
                      className="border-slate-600 text-slate-300 hover:bg-slate-600"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      تصدير البيانات
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Timeline Tab */}
            <TabsContent value="timeline" className="p-2 sm:p-4">
              <Card className="bg-gradient-to-br from-slate-700/50 to-slate-800/50 border-slate-600 shadow-lg">
                <CardHeader className="p-3 sm:p-6">
                  <CardTitle className="text-white text-base sm:text-lg flex items-center gap-2">
                    <div className="p-2 bg-blue-500/20 rounded-lg">
                      <Clock className="h-4 w-4 sm:h-5 sm:w-5 text-blue-400" />
                    </div>
                    سجل الأحداث
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-3 sm:p-6">
                  <div className="space-y-4 sm:space-y-6">
                    {order.timeline.map((event, index) => (
                      <div key={event.id} className="flex gap-3 sm:gap-4">
                        {/* Timeline indicator */}
                        <div className="flex flex-col items-center flex-shrink-0">
                          <div className={`w-4 h-4 sm:w-5 sm:h-5 rounded-full border-2 border-slate-800 shadow-lg ${
                            event.type === "created" ? "bg-blue-500" :
                            event.type === "status_change" ? "bg-yellow-500" :
                            event.type === "admin_note" ? "bg-purple-500" :
                            "bg-gray-500"
                          }`} />
                          {index < order.timeline.length - 1 && (
                            <div className="w-0.5 h-8 sm:h-12 bg-gradient-to-b from-slate-600 to-slate-700 mt-2" />
                          )}
                        </div>

                        {/* Event content */}
                        <div className="flex-1 pb-4 min-w-0">
                          <div className="bg-slate-800/50 rounded-lg p-3 sm:p-4 border border-slate-700/50 shadow-sm">
                            <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 mb-2">
                              <p className="text-white font-medium text-sm sm:text-base break-words">{event.description}</p>
                              <Badge
                                variant="outline"
                                className={`text-xs flex-shrink-0 ${
                                  event.type === "created" ? "text-blue-400 border-blue-400/30" :
                                  event.type === "status_change" ? "text-yellow-400 border-yellow-400/30" :
                                  event.type === "admin_note" ? "text-purple-400 border-purple-400/30" :
                                  "text-gray-400 border-gray-400/30"
                                }`}
                              >
                                {event.type === "created" ? "إنشاء" :
                                 event.type === "status_change" ? "تغيير حالة" :
                                 event.type === "admin_note" ? "ملاحظة إدارية" :
                                 "حدث"}
                              </Badge>
                            </div>

                            <div className="flex flex-col sm:flex-row sm:items-center gap-2 text-slate-400 text-xs sm:text-sm">
                              <span className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {formatDateTime(event.createdAt)}
                              </span>
                              {event.createdBy && (
                                <span className="flex items-center gap-1">
                                  <User className="h-3 w-3" />
                                  {event.createdBy}
                                </span>
                              )}
                            </div>

                            {/* Event details */}
                            {event.details && Object.keys(event.details).length > 0 && (
                              <div className="mt-3 p-3 bg-slate-700/50 rounded-lg border border-slate-600/50">
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                                  {Object.entries(event.details).map(([key, value]) => (
                                    <div key={key} className="text-slate-300 text-xs sm:text-sm">
                                      <span className="font-medium text-slate-200">{key}:</span>
                                      <span className="mr-2 break-words">{String(value)}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}

                    {order.timeline.length === 0 && (
                      <div className="text-center py-8">
                        <Clock className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                        <p className="text-slate-400">لا توجد أحداث في السجل</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Actions Tab */}
            <TabsContent value="actions" className="p-2 sm:p-4 space-y-4 sm:space-y-6">
              {/* Quick Actions */}
              <Card className="bg-gradient-to-br from-slate-700/50 to-slate-800/50 border-slate-600 shadow-lg">
                <CardHeader className="p-3 sm:p-6">
                  <CardTitle className="text-white text-base sm:text-lg flex items-center gap-2">
                    <div className="p-2 bg-green-500/20 rounded-lg">
                      <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-400" />
                    </div>
                    إجراءات سريعة
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-3 sm:p-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
                    {/* Approve Order */}
                    {order.status === "pending" && userRole !== "viewer" && (
                      <Button
                        onClick={() => handleStatusUpdate("processing", "تم قبول الطلب وبدء المعالجة")}
                        disabled={isLoading}
                        className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-lg hover:shadow-xl transition-all duration-200 text-sm sm:text-base"
                      >
                        <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                        <span className="hidden sm:inline">قبول الطلب</span>
                        <span className="sm:hidden">قبول</span>
                      </Button>
                    )}

                    {/* Complete Order */}
                    {order.status === "processing" && userRole !== "viewer" && (
                      <Button
                        onClick={() => handleStatusUpdate("completed", "تم إنجاز الطلب بنجاح")}
                        disabled={isLoading}
                        className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl transition-all duration-200 text-sm sm:text-base"
                      >
                        <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                        <span className="hidden sm:inline">إنجاز الطلب</span>
                        <span className="sm:hidden">إنجاز</span>
                      </Button>
                    )}

                    {/* Reject Order */}
                    {(order.status === "pending" || order.status === "processing") && userRole !== "viewer" && (
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="destructive"
                            disabled={isLoading}
                          >
                            <XCircle className="h-4 w-4 mr-2" />
                            رفض الطلب
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent className="bg-slate-800 border-slate-700">
                          <AlertDialogHeader>
                            <AlertDialogTitle className="text-white">تأكيد رفض الطلب</AlertDialogTitle>
                            <AlertDialogDescription className="text-slate-400">
                              هل أنت متأكد من رفض هذا الطلب؟ سيتم إشعار العميل برفض الطلب.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel className="bg-slate-700 text-white border-slate-600">
                              إلغاء
                            </AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleStatusUpdate("failed", "تم رفض الطلب من قبل الإدارة")}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              تأكيد الرفض
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    )}

                    {/* Delete Order */}
                    {userRole === "admin" && (
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="destructive"
                            disabled={isLoading}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            حذف الطلب
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent className="bg-slate-800 border-slate-700">
                          <AlertDialogHeader>
                            <AlertDialogTitle className="text-white">تأكيد حذف الطلب</AlertDialogTitle>
                            <AlertDialogDescription className="text-slate-400">
                              هل أنت متأكد من حذف هذا الطلب؟ هذا الإجراء لا يمكن التراجع عنه.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel className="bg-slate-700 text-white border-slate-600">
                              إلغاء
                            </AlertDialogCancel>
                            <AlertDialogAction
                              onClick={handleDeleteOrder}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              تأكيد الحذف
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Status Management */}
              <Card className="bg-gradient-to-br from-slate-700/50 to-slate-800/50 border-slate-600 shadow-lg">
                <CardHeader className="p-3 sm:p-6">
                  <CardTitle className="text-white text-base sm:text-lg flex items-center gap-2">
                    <div className="p-2 bg-yellow-500/20 rounded-lg">
                      <AlertCircle className="h-4 w-4 sm:h-5 sm:w-5 text-yellow-400" />
                    </div>
                    إدارة الحالة والأولوية
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 sm:space-y-6 p-3 sm:p-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                    <div className="space-y-2">
                      <Label className="text-slate-300 text-sm sm:text-base font-medium">تغيير الحالة</Label>
                      <Select
                        value={selectedStatus}
                        onValueChange={(value) => setSelectedStatus(value as OrderStatus)}
                        disabled={userRole === "viewer"}
                      >
                        <SelectTrigger className="bg-slate-600 border-slate-500 text-white hover:bg-slate-500 transition-colors">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-slate-700 border-slate-600">
                          <SelectItem value="pending" className="hover:bg-slate-600">
                            <div className="flex items-center gap-2">
                              <Clock className="h-4 w-4 text-yellow-400" />
                              في الانتظار
                            </div>
                          </SelectItem>
                          <SelectItem value="processing" className="hover:bg-slate-600">
                            <div className="flex items-center gap-2">
                              <AlertCircle className="h-4 w-4 text-blue-400" />
                              قيد المعالجة
                            </div>
                          </SelectItem>
                          <SelectItem value="completed" className="hover:bg-slate-600">
                            <div className="flex items-center gap-2">
                              <CheckCircle className="h-4 w-4 text-green-400" />
                              مكتمل
                            </div>
                          </SelectItem>
                          <SelectItem value="failed" className="hover:bg-slate-600">
                            <div className="flex items-center gap-2">
                              <XCircle className="h-4 w-4 text-red-400" />
                              فشل
                            </div>
                          </SelectItem>
                          <SelectItem value="cancelled" className="hover:bg-slate-600">
                            <div className="flex items-center gap-2">
                              <XCircle className="h-4 w-4 text-gray-400" />
                              ملغي
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label className="text-slate-300 text-sm sm:text-base font-medium">الأولوية</Label>
                      <Select
                        value={selectedPriority}
                        onValueChange={setSelectedPriority}
                        disabled={userRole === "viewer"}
                      >
                        <SelectTrigger className="bg-slate-600 border-slate-500 text-white hover:bg-slate-500 transition-colors">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-slate-700 border-slate-600">
                          <SelectItem value="low" className="hover:bg-slate-600">
                            <div className="flex items-center gap-2">
                              <Flag className="h-4 w-4 text-gray-400" />
                              منخفض
                            </div>
                          </SelectItem>
                          <SelectItem value="normal" className="hover:bg-slate-600">
                            <div className="flex items-center gap-2">
                              <Flag className="h-4 w-4 text-blue-400" />
                              عادي
                            </div>
                          </SelectItem>
                          <SelectItem value="high" className="hover:bg-slate-600">
                            <div className="flex items-center gap-2">
                              <Flag className="h-4 w-4 text-orange-400" />
                              عالي
                            </div>
                          </SelectItem>
                          <SelectItem value="urgent" className="hover:bg-slate-600">
                            <div className="flex items-center gap-2">
                              <Flag className="h-4 w-4 text-red-400" />
                              عاجل
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {(selectedStatus !== order.status || selectedPriority !== (order.priority || "normal")) && userRole !== "viewer" && (
                    <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t border-slate-600">
                      {selectedStatus !== order.status && (
                        <Button
                          onClick={() => handleStatusUpdate(selectedStatus)}
                          disabled={isLoading}
                          className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                        >
                          <Save className="h-4 w-4 mr-2" />
                          تحديث الحالة
                        </Button>
                      )}

                      {selectedPriority !== (order.priority || "normal") && (
                        <Button
                          onClick={handleSaveUpdates}
                          disabled={isLoading}
                          variant="outline"
                          className="border-slate-600 text-slate-300 hover:bg-slate-600"
                        >
                          <Flag className="h-4 w-4 mr-2" />
                          تحديث الأولوية
                        </Button>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Notes Management */}
              <Card className="bg-slate-700/50 border-slate-600">
                <CardHeader>
                  <CardTitle className="text-white text-lg flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    إدارة الملاحظات
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label className="text-slate-300">ملاحظات للعميل</Label>
                    <Textarea
                      value={adminNotes}
                      onChange={(e) => setAdminNotes(e.target.value)}
                      placeholder="ملاحظات ستكون مرئية للعميل..."
                      className="bg-slate-600 border-slate-500 text-white"
                      rows={3}
                      disabled={userRole === "viewer"}
                    />
                  </div>

                  <div>
                    <Label className="text-slate-300">ملاحظات داخلية</Label>
                    <Textarea
                      value={internalNotes}
                      onChange={(e) => setInternalNotes(e.target.value)}
                      placeholder="ملاحظات داخلية للإدارة فقط..."
                      className="bg-slate-600 border-slate-500 text-white"
                      rows={3}
                      disabled={userRole === "viewer"}
                    />
                  </div>

                  <div>
                    <Label className="text-slate-300">تعيين للمشرف</Label>
                    <Input
                      value={assignedAdmin}
                      onChange={(e) => setAssignedAdmin(e.target.value)}
                      placeholder="معرف المشرف المسؤول..."
                      className="bg-slate-600 border-slate-500 text-white"
                      disabled={userRole === "viewer"}
                    />
                  </div>

                  {userRole !== "viewer" && (
                    <Button
                      onClick={handleSaveUpdates}
                      disabled={isLoading}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      حفظ التحديثات
                    </Button>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Chat Tab */}
            <TabsContent value="chat" className="p-2 sm:p-4">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white mb-4">محادثة العميل</h3>
                <SimpleOrderChatWidget order={order} />

                {/* Quick Info */}
                <div className="bg-slate-700/30 rounded-lg p-4 border border-slate-600">
                  <h4 className="text-white font-medium mb-2">معلومات سريعة</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                    <div className="text-slate-300">
                      <span className="text-slate-400">العميل:</span> {order.userDetails.name || "غير محدد"}
                    </div>
                    <div className="text-slate-300">
                      <span className="text-slate-400">المنتج:</span> {order.templateName}
                    </div>
                    <div className="text-slate-300">
                      <span className="text-slate-400">البريد:</span> {order.userDetails.email}
                    </div>
                    <div className="text-slate-300">
                      <span className="text-slate-400">رقم الطلب:</span> #{order.id}
                    </div>
                  </div>
                </div>

                <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <MessageSquare className="h-4 w-4 text-blue-400" />
                    <span className="text-blue-300 font-medium">اختصار المحادثة</span>
                  </div>
                  <p className="text-blue-200 text-sm">
                    هذا اختصار سريع لفتح محادثة العميل. ستفتح نافذة المحادثة الرئيسية مع تحديد العميل تلقائياً،
                    ويمكنك التنقل بين جميع المحادثات الأخرى بشكل طبيعي.
                  </p>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
