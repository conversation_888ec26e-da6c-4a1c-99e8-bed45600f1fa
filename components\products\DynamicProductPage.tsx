"use client"

import React, { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { DesktopFooter } from "@/components/layout/DesktopFooter"
import {
  ArrowLeft,
  Star,
  Clock,
  Zap,
  Shield,
  Check,
  Gift,
  Sparkles,
  Key,
  Download
} from "lucide-react"
// Removed ProductTemplate and related imports - will be rebuilt

interface DynamicProductPageProps {
  productId: string
}

export function DynamicProductPage({ productId }: DynamicProductPageProps) {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("shop")
  const [isMenuO<PERSON>, setIsMenuOpen] = useState(false)
  const [product, setProduct] = useState<any>(null) // Will be rebuilt
  const [loading, setLoading] = useState(true)

  // Removed product loading - will be rebuilt
  useEffect(() => {
    setLoading(false)
    // TODO: Implement new product loading system
  }, [productId])

  // Navigation handler
  const handleTabChange = (tab: string) => {
    if (tab === "wallet") {
      router.push("/wallet")
    } else if (tab === "profile") {
      router.push("/profile")
    } else if (tab === "shop") {
      router.push("/shop")
    } else if (tab === "home") {
      router.push("/")
    } else if (tab === "support") {
      router.push("/contact")
    } else {
      setActiveTab(tab)
    }
  }

  // Handle form submission (purchase)
  const handlePurchase = (formData: Record<string, any>) => {
    console.log('Purchase data:', formData)
    // TODO: Implement actual purchase logic
    // For now, redirect to checkout or show success message
    router.push('/checkout/success')
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">⏳</div>
          <h3 className="text-xl font-bold text-white mb-2">جاري تحميل المنتج...</h3>
          <p className="text-slate-400">يرجى الانتظار</p>
        </div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">❌</div>
          <h3 className="text-xl font-bold text-white mb-2">المنتج غير موجود</h3>
          <p className="text-slate-400 mb-6">لم نتمكن من العثور على المنتج المطلوب</p>
          <Button onClick={() => router.push('/shop')} className="bg-yellow-500 hover:bg-yellow-600 text-slate-900">
            العودة للمتجر
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white">
      {/* Background Pattern */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl" />
      </div>

      <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />
      <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

      <main className="container mx-auto px-4 py-8 relative z-10">
        {/* Back Button */}
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-6 text-slate-300 hover:text-white hover:bg-slate-800/50"
        >
          <ArrowLeft className="h-4 w-4 ml-2" />
          العودة
        </Button>

        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            
            {/* Left Column - Product Image & Info */}
            <div className="space-y-6">
              {/* Product Image */}
              <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm overflow-hidden">
                <CardContent className="p-0">
                  <div className="relative aspect-square">
                    {product.previewImage ? (
                      <img
                        src={product.previewImage}
                        alt={product.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-slate-700 via-slate-600 to-slate-800 flex items-center justify-center">
                        <Gift className="h-24 w-24 text-slate-400" />
                      </div>
                    )}
                    
                    {/* Badges */}
                    <div className="absolute top-4 right-4 space-y-2">
                      {/* Digital Product Badge */}
                      {product.productType === "digital" && (
                        <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold animate-pulse">
                          <Sparkles className="h-3 w-3 mr-1" />
                          منتج رقمي
                        </Badge>
                      )}
                      
                      <Badge 
                        className={`${
                          product.productType === "digital"
                            ? 'bg-purple-500/20 text-purple-400 border-purple-500/30'
                            : product.digitalConfig?.autoDeliver
                            ? 'bg-green-500/20 text-green-400 border-green-500/30' 
                            : 'bg-blue-500/20 text-blue-400 border-blue-500/30'
                        }`}
                      >
                        {product.productType === "digital" ? (
                          <>
                            <Download className="h-3 w-3 mr-1" />
                            تسليم فوري
                          </>
                        ) : product.digitalConfig?.autoDeliver ? (
                          <>
                            <Zap className="h-3 w-3 mr-1" />
                            شحن فوري
                          </>
                        ) : (
                          <>
                            <Clock className="h-3 w-3 mr-1" />
                            شحن يدوي
                          </>
                        )}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Product Features */}
              <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
                <CardContent className="p-6">
                  <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                    <Shield className="h-5 w-5 text-green-400" />
                    مميزات المنتج
                  </h3>
                  <div className="space-y-3">
                    {product.productType === "digital" ? (
                      <>
                        <div className="flex items-center gap-3">
                          <Check className="h-4 w-4 text-green-400 flex-shrink-0" />
                          <span className="text-slate-300 text-sm">🚀 تسليم فوري للمحتوى الرقمي</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <Check className="h-4 w-4 text-green-400 flex-shrink-0" />
                          <span className="text-slate-300 text-sm">🔒 محتوى محمي ومشفر</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <Check className="h-4 w-4 text-green-400 flex-shrink-0" />
                          <span className="text-slate-300 text-sm">💰 أسعار تنافسية</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <Check className="h-4 w-4 text-green-400 flex-shrink-0" />
                          <span className="text-slate-300 text-sm">📱 عرض في المحفظة الرقمية</span>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="flex items-center gap-3">
                          <Check className="h-4 w-4 text-green-400 flex-shrink-0" />
                          <span className="text-slate-300 text-sm">⚡ شحن سريع وآمن</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <Check className="h-4 w-4 text-green-400 flex-shrink-0" />
                          <span className="text-slate-300 text-sm">🛡️ ضمان الجودة</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <Check className="h-4 w-4 text-green-400 flex-shrink-0" />
                          <span className="text-slate-300 text-sm">💬 دعم فني متخصص</span>
                        </div>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Right Column - Product Form (TO BE REBUILT) */}
            <div className="space-y-6">
              <div className="bg-slate-800/50 border border-slate-700 rounded-lg p-6">
                <h3 className="text-white text-lg font-semibold mb-4">Product Form</h3>
                <p className="text-slate-400">Product form will be rebuilt from scratch</p>
              </div>
            </div>
          </div>
        </div>
      </main>

      <MobileNavigation activeTab={activeTab} onTabChange={handleTabChange} />
      <DesktopFooter activeTab={activeTab} onTabChange={handleTabChange} />
    </div>
  )
}
